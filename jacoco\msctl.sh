#!/bin/bash
action=$1
target=$2
args=$@

source ~/.msrc >/dev/null
MS_BASE=${MS_BASE:-/opt}
COMPOSE_FILES=$(cat ${MS_BASE}/metersphere/compose_files 2>/dev/null || echo "")
source ${MS_BASE}/metersphere/install.conf
export COMPOSE_HTTP_TIMEOUT=180

function usage() {
  echo "MeterSphere 控制脚本"
  echo
  echo "Usage: "
  echo "  ./msctl.sh [COMMAND] [ARGS...]"
  echo "  ./msctl.sh --help"
  echo
  echo "Commands: "
  echo "  status    查看 MeterSphere 服务运行状态"
  echo "  start     启动 MeterSphere 服务"
  echo "  stop      停止 MeterSphere 服务"
  echo "  restart   重启 MeterSphere 服务"
  echo "  reload    重新加载 MeterSphere 服务"
  echo "  upgrade   升级 MeterSphere 至最新版本"
  echo "  upgrade [RELEASE]  根据版本号搜索离线包，升级 MeterSphere 至对应版本"
  echo "  uninstall 卸载 MeterSphere 服务"
  echo "  version   查看 MeterSphere 版本信息"
}

function generate_compose_files() {
  compose_files="-f docker-compose-base.yml"
  mkdir -p ${MS_BASE}/metersphere/data/jmeter
  mkdir -p ${MS_BASE}/metersphere/data/body
  case ${MS_INSTALL_MODE} in
  allinone)
    compose_files="${compose_files} -f docker-compose-server.yml -f docker-compose-node-controller.yml"
    ;;
  server)
    compose_files="${compose_files} -f docker-compose-server.yml"
    ;;
  node-controller)
    compose_files="${compose_files} -f docker-compose-node-controller.yml"
    ;;
  *)
    log "... 不支持的安装模式，请从 [ allinone | server | node-controller ] 中进行选择"
    ;;
  esac
  if [ ${MS_INSTALL_MODE} != "node-controller" ]; then
    # 是否使用外部数据库
    if [ ${MS_EXTERNAL_MYSQL} = "false" ]; then
      mkdir -p ${MS_BASE}/metersphere/data/mysql
      chmod 655 ${MS_BASE}/metersphere/conf/my.cnf
      compose_files="${compose_files} -f docker-compose-mysql.yml"
    fi
    # 是否使用外部 Kafka
    if [ ${MS_EXTERNAL_KAFKA} = "false" ]; then
      mkdir -p ${MS_BASE}/metersphere/data/kafka
      compose_files="${compose_files} -f docker-compose-kafka.yml"
    fi
    # 是否使用外部 Prometheus
    if [ ${MS_EXTERNAL_PROM} = "false" ]; then
      mkdir -p ${MS_BASE}/metersphere/data/prometheus
      compose_files="${compose_files} -f docker-compose-prometheus.yml"
    fi
    # 是否使用外部 Redis
    if [ ${MS_EXTERNAL_REDIS} = "false" ]; then
      mkdir -p ${MS_BASE}/metersphere/data/redis
      compose_files="${compose_files} -f docker-compose-redis.yml"
    fi
    # 是否创建 seleniarm 容器
    if [ ${MS_SELENIARM_ENABLED} = "true" ]; then
      compose_files="${compose_files} -f docker-compose-seleniarm.yml"
    fi
  fi
  echo ${compose_files} >${MS_BASE}/metersphere/compose_files
  chmod +rw -R ${MS_BASE}/metersphere
  COMPOSE_FILES=$(cat ${MS_BASE}/metersphere/compose_files 2>/dev/null || echo "")
}

function download() {
  # wget -nv -T 60 -t 1 --no-check-certificate https://github.com/metersphere/metersphere/releases/download/${MS_LATEST_VERSION}/metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz -O /tmp/metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz
  git_urls=('github.com' 'hub.fastgit.org' 'ghproxy.com/https://github.com')

  for git_url in ${git_urls[*]}; do
    success="true"
    for i in {1..3}; do
      echo -ne "检测 ${git_url} ... ${i} "
      curl -m 5 -kIs https://${git_url} >/dev/null
      if [ $? != 0 ]; then
        echo "failed"
        success="false"
        break
      else
        echo "ok"
      fi
    done
    if [ ${success} == "true" ]; then
      server_url=${git_url}
      break
    fi
  done

  if [ "x${server_url}" == "x" ]; then
    echo "没有找到稳定的下载服务器，请稍候重试"
    exit 1
  fi
  echo "使用下载服务器 ${server_url}"
  cd /tmp
  wget -nv -T 60 -t 1 --no-check-certificate https://${server_url}/metersphere/metersphere/releases/download/${MS_LATEST_VERSION}/metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz -O /tmp/metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz
  if [ $? -ne 0 ]; then
    echo -e "\e[31m升级失败:连接下载服务器超时！\n可手动下载升级包，然后执行\e[1;33m msctl upgrade ${MS_LATEST_VERSION} \e[0;31m离线升级\e[0m"
    return 2
  fi
}

function status() {
  echo
  cd ${MS_BASE}/metersphere
  docker-compose ${COMPOSE_FILES} ps
}
function start() {
  echo
  cd ${MS_BASE}/metersphere
  docker-compose ${COMPOSE_FILES} start ${target}
}
function stop() {
  echo
  cd ${MS_BASE}/metersphere
  docker-compose ${COMPOSE_FILES} stop ${target}
}
function restart() {
  echo
  cd ${MS_BASE}/metersphere
  docker-compose ${COMPOSE_FILES} stop ${target}
  docker-compose ${COMPOSE_FILES} start ${target}
}
function reload() {
  echo
  cd ${MS_BASE}/metersphere
  docker-compose ${COMPOSE_FILES} up -d --remove-orphans
}
function uninstall() {
  echo
  cd ${MS_BASE}/metersphere
  docker-compose ${COMPOSE_FILES} down ${target}
}
function version() {
  echo
  cat ${MS_BASE}/metersphere/version
}
function upgrade() {
  if [ -z "$target" ]; then
    curl -s https://api.github.com/repos/metersphere/metersphere/releases >/dev/null
    if [ $? -ne 0 ]; then
      echo -e "\e[31m获取最新版本信息失败,请检查服务器到GitHub的网络连接是否正常！\e[0m"
      return 2
    fi
    MS_VERSION=$(cat ${MS_BASE}/metersphere/version)
    export MS_VERSION=${MS_VERSION%-*}
    echo -e "\e[32m 检测当前版本为\e[1;33m${MS_VERSION} \e[0m"
    python - <<EOF
# -*- coding: UTF-8 -*-
import os
import json
import re

latest_release=""
release_pattern=""

# 判断是否是LTS版本
current_version=os.environ.get("MS_VERSION")
if current_version.endswith("-lts") or current_version.startswith("v1."):
  release_pattern="v\d+\.\d+\.\d+-lts$"
else:
  release_pattern="v\d+\.\d+\.\d+$"

def get_releases(page):
  # 根据是否是LTS版本获取对应的最新版本号
  try:
      releases=os.popen("curl -s https://api.github.com/repos/metersphere/metersphere/releases?page=%d" % (page)).read()
      releases=[ x["name"] for x in json.loads(releases) if x["prerelease"] == False ]
  except Exception as e:
      print(str(e))
      print("获取Release信息失败，请检查服务器到GitHub的网络连接是否正常")
      exit(1)
  else:
      for release in releases:
          if re.search(release_pattern,release) != None:
            return release

page = 1
while (page <= 10):
  latest_release = get_releases(page)
  if (latest_release != "" and latest_release != None):
    break
  page += 1

# 记录最新版本号
os.popen("echo "+latest_release+" > /tmp/ms_latest_release")

EOF

    MS_LATEST_VERSION=$(cat /tmp/ms_latest_release)
    if [ "${MS_LATEST_VERSION}" = "" ]; then
      echo -e "未获取到最新版本"
      exit 1
    elif [ "${MS_LATEST_VERSION}" = "${MS_VERSION}" ]; then
      echo -e "最新版本与当前版本一致,退出升级过程"
      exit 0
    else
      echo -e "\e[32m 检测到GitHub上最新版本为\e[1;33m${MS_LATEST_VERSION}\e[0;32m 即将执行在线升级...\e[0m"
    fi
    sleep 5s

    download
  else
    MS_LATEST_VERSION=${target}
    download
  fi

  if [ ! -f "/tmp/metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz" ]; then
    if [ ! -f "/tmp/metersphere-offline-installer-${MS_LATEST_VERSION}.tar.gz" ]; then
      echo -e "\e[31m未找到升级包\e[1;33m/tmp/metersphere-*-installer-${MS_LATEST_VERSION}.tar.gz\e[31m，请检查！\e[0m"
      echo -e "参考下载地址：\e[4;7mwget -T60 -t1 --no-check-certificate https://github.com/metersphere/metersphere/releases/download/${MS_LATEST_VERSION}/metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz -O /tmp/metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz\e[0m"
      return 2
    fi
  fi

  cd /tmp
  tar zxvf metersphere-online-installer-${MS_LATEST_VERSION}.tar.gz
  cd metersphere-online-installer-${MS_LATEST_VERSION}
  /bin/bash install.sh
  rm -rf /tmp/metersphere-online-installer-${MS_LATEST_VERSION}
}

function main() {
  case "${action}" in
  status)
    status
    ;;
  start)
    start
    ;;
  stop)
    stop
    ;;
  restart)
    restart
    ;;
  reload)
    generate_compose_files
    reload
    ;;
  upgrade)
    upgrade
    ;;
  uninstall)
    uninstall
    ;;
  version)
    version
    ;;
  help)
    usage
    ;;
  --help)
    usage
    ;;
  generate_compose_files)
    generate_compose_files
    ;;
  *)
    echo
    cd ${MS_BASE}/metersphere
    docker-compose ${COMPOSE_FILES} $@
    ;;
  esac
}
main $@