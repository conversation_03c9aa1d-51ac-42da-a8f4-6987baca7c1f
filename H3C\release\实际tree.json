[{"type": "directory", "name": ".", "contents": [{"type": "directory", "name": "53G7-RG800", "contents": [{"type": "directory", "name": "<PERSON>lov5", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "modelzoo_ubuntu_x86.tar.gz"}, {"type": "file", "name": "yolov5.tar.gz"}]}]}]}]}, {"type": "directory", "name": "55G6_7-P800", "contents": [{"type": "directory", "name": "Baichuan2-13B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "Baichuan2-13B-Chat.tar.gz"}, {"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}]}, {"type": "directory", "name": "Baichuan2-7B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "Baichuan2-7B-Chat.tar.gz"}, {"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}, {"type": "directory", "name": "Pre-training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "baichuan2_7b_data.tar"}, {"type": "file", "name": "baichuan2-7B.tgz"}, {"type": "file", "name": "klms-train-ubuntu20.04-train-kl3-dev-3.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "tokenizer.tgz"}]}]}]}, {"type": "directory", "name": "ChatGLM2-6B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "chatglm2-6b.tar.gz"}, {"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "chatglm2-6b.tar.gz"}, {"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}, {"type": "directory", "name": "SFT_fine-tuning", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "Chatglm2-6B-ptuning.tar.gz"}, {"type": "file", "name": "chatglm2-6b.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "xmlir_v0.29.tar"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "Chatglm2-6B-ptuning.tar.gz"}, {"type": "file", "name": "chatglm2-6b.tar.gz"}, {"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}]}]}]}, {"type": "directory", "name": "ChatGLM3-6B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "chatglm_infer_ubuntu2004_x86.tar.gz"}, {"type": "file", "name": "inference_kl3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "model.tar.gz"}]}]}]}, {"type": "directory", "name": "DeepSeek-coder-33B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "deepseek-coder-33b-instruct.tar.gz"}, {"type": "file", "name": "deepseek-coder-6.7b-base_kl3_xtrt-llm_infer_x86.tar.gz"}, {"type": "file", "name": "docker_ubuntu2004_x86_64_xpytorch201_0.0.5.tar.gz"}, {"type": "file", "name": "human-eval.tar.gz"}, {"type": "file", "name": "md5sums.txt"}]}]}]}, {"type": "directory", "name": "Deepseek-coder-6.7B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "deepseek-coder-6.7b-base_hugging_face.tar.gz"}, {"type": "file", "name": "deepseek-coder-6.7b-base_kl3_xtrt-llm_infer_x86.tar.gz"}, {"type": "file", "name": "docker_ubuntu2004_x86_64_xpytorch201_0.0.5.tar.gz"}, {"type": "file", "name": "human-eval.tar.gz"}, {"type": "file", "name": "md5sums.txt"}]}]}]}, {"type": "directory", "name": "DeepSeek-R1-671B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "DeepSeek-R1-0220-engine.tar.gz"}, {"type": "file", "name": "deepseek_server.tar.gz"}, {"type": "directory", "name": "fw", "contents": [{"type": "file", "name": "kl3_pbl_1_0.ota"}, {"type": "file", "name": "kl3_pciefw_2_4.ota"}, {"type": "file", "name": "kl3_sbl_1_31.ota"}]}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "output_xtrt_llm_20250220.tar.gz"}, {"type": "file", "name": "pressure_test_v5.tar.gz"}, {"type": "file", "name": "pressure_test_v6.tar.gz"}, {"type": "file", "name": "xre-Linux-x86_64_1210.run"}, {"type": "file", "name": "xtrtllm_ubuntu_2004_x86_64_deepseek-v3_v8.tar.gz"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "deepseek_server.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "output_xtrt_llm_20250226.tar.gz"}, {"type": "file", "name": "pressure_test_v5.tar.gz"}, {"type": "file", "name": "pressure_test_v6.tar.gz"}, {"type": "file", "name": "xtrtllm_ubuntu_2004_x86_64_deepseek-v3_v14.tar.gz"}]}, {"type": "directory", "name": "v3.0", "contents": [{"type": "file", "name": "klms-xtrtllm-ubuntu20.04.deepseek_0328.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "pressure_test_v6_1.tar.gz"}]}, {"type": "directory", "name": "v4.0", "contents": [{"type": "file", "name": "klms-xtrtllm-ubuntu20.04.deepseek_20250407.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "pressure_test_v6_1.tar.gz"}]}]}]}, {"type": "directory", "name": "Llama2-13B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "Llama-2-13b-chat-hf.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202405.14-py3-none-any.whl"}]}]}, {"type": "directory", "name": "Pre-training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-train-ubuntu20.04-train-kl3-dev-2.0.tar"}, {"type": "file", "name": "llama2-13B_v1.0.0.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "tokenizer.model"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "llama2-13B_v1.0.0.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "tokenizer.model"}]}]}]}, {"type": "directory", "name": "Llama2-70B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "Llama-2-70b-chat-hf.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}, {"type": "directory", "name": "Pre-training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-train-ubuntu20.04-train-kl3-dev-llama2-70B-1.0.tar"}, {"type": "file", "name": "llama2-70B_v1.0.0.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "tokenizer.model"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "llama2-70B_v1.0.0.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "tokenizer.model"}]}]}]}, {"type": "directory", "name": "Llama2-7B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "Llama-2-7b-chat-hf.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202405.14-py3-none-any.whl"}]}]}, {"type": "directory", "name": "Pre-training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-train-ubuntu20.04-train-kl3-dev-2.0.tar"}, {"type": "file", "name": "llama2-7b_v1.0.0.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "tokenizer.model"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "llama2-7b_v1.0.0.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "tokenizer.model"}]}]}]}, {"type": "directory", "name": "Llama3-70B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-xpeft-ubuntu20.04.202407-kl3-dev.1.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "Meta-Llama-3-70B-Instruct.tar.gz"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202405.14-py3-none-any.whl"}]}]}]}, {"type": "directory", "name": "Llama3-8B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "Meta-Llama-3-8B-Instruct.tar.gz"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "run_perf.sh"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}, {"type": "directory", "name": "Pre-training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "Megatron.tar"}, {"type": "file", "name": "Meta-Llama-3-8B.tar.gz"}, {"type": "file", "name": "pile_test.tar.gz"}]}]}]}, {"type": "directory", "name": "Qwen-14B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "Qwen-14B-Chat.tar.gz"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "Qwen-14B-Chat.tar.gz"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}, {"type": "directory", "name": "Pre-Training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-train-ubuntu20.04-train-kl3-dev-2.0.tar"}, {"type": "file", "name": "punkt.zip"}, {"type": "file", "name": "QWen-14B_v1.0.0.tgz"}, {"type": "file", "name": "qwen.tiktoken"}, {"type": "file", "name": "xccl_linux_for_qwen_14b_2_machines_v1.tar"}, {"type": "file", "name": "xpytorch-cp38-torch201-ubuntu2004-x64-qwen14b-0507.run"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "punkt.zip"}, {"type": "file", "name": "QWen-14B_v1.0.0.tgz"}, {"type": "file", "name": "qwen.tiktoken"}, {"type": "file", "name": "xccl_linux_for_qwen_14b_2_machines_v1.tar"}, {"type": "file", "name": "xpytorch-cp38-torch201-ubuntu2004-x64-qwen14b-0507.run"}]}]}]}, {"type": "directory", "name": "Qwen1.5-14B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "Qwen1.5-14B-Chat.tar.gz"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}]}, {"type": "directory", "name": "Qwen1.5-32B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "Qwen1.5-32B-Chat.tar.gz"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}]}, {"type": "directory", "name": "Qwen1.5-72B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-xtrtllm-ubuntu20.04.d6bf14c-kl3-dev-1.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "Qwen1.5-72B-Chat.tar.gz"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}]}, {"type": "directory", "name": "Qwen-7B", "contents": [{"type": "directory", "name": "Inference", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "OpenCompassData-core-20231110.zip"}, {"type": "file", "name": "opencompass.tar.gz"}, {"type": "file", "name": "Qwen-7B.tar.gz"}, {"type": "file", "name": "xtrtllm_runner-202406.1-py3-none-any.whl"}]}]}, {"type": "directory", "name": "Pre-training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "klms-train-ubuntu20.04-train-kl3-dev-2.0.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "QWen-7B_v1.0.0.tgz"}, {"type": "file", "name": "qwen.tiktoken"}, {"type": "file", "name": "xpytorch-cp38-torch201-ubuntu2004-x64.run"}]}, {"type": "directory", "name": "v2.0", "contents": [{"type": "file", "name": "kiss-xpu-pytorch-xtrt.ubuntu20.04-202408-kl3-dev.3.tar.gz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "punkt.zip"}, {"type": "file", "name": "QWen-7B_v1.0.0.tgz"}, {"type": "file", "name": "qwen.tiktoken"}]}]}]}, {"type": "directory", "name": "Stable_diffusion_2.1", "contents": [{"type": "directory", "name": "Inferece", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "datasets.tgz"}, {"type": "file", "name": "klms-xpeft-ubuntu20.04.202406-kl3-dev.2.tar"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "models.tgz"}, {"type": "file", "name": "stable_diffusion.tgz"}, {"type": "file", "name": "xft_v1.0.0.tar.gz"}]}]}, {"type": "directory", "name": "Pre-training", "contents": [{"type": "directory", "name": "v1.0", "contents": [{"type": "file", "name": "8_card_config.yaml"}, {"type": "file", "name": "diffusers.tgz"}, {"type": "file", "name": "md5sums.txt"}, {"type": "file", "name": "pack_dependency.run"}, {"type": "file", "name": "stable-diffusion-2-1-base.tgz"}, {"type": "file", "name": "stable_diffusion_2_1_v5.tgz"}, {"type": "file", "name": "xmlir_v0.28.tar"}, {"type": "file", "name": "xpytorch-cp38-torch201-ubuntu2004-x64.run"}]}]}]}]}]}, {"type": "report", "directories": 89, "files": 217}]